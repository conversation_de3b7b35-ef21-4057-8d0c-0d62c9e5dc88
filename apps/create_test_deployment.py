from prefect import flow, task, get_client
import asyncio

@task
def process_data(environment, tenant_key=None, restaurant_key=None):
    """Process data for a specific environment, tenant, and restaurant"""
    print(f"Processing data for environment: {environment}")
    if tenant_key:
        print(f"Tenant key: {tenant_key}")
    if restaurant_key:
        print(f"Restaurant key: {restaurant_key}")
    return {"status": "success", "environment": environment}

@flow(name="Test Google Reviews Pipeline")
def google_reviews_pipeline(environment="DEV", dim_tenant_key=None, dim_restaurant_key=None):
    """A test flow that simulates the Google Reviews pipeline"""
    print(f"Starting Google Reviews pipeline for {environment}")
    result = process_data(environment, dim_tenant_key, dim_restaurant_key)
    print("Pipeline completed successfully")
    return result

async def create_work_pool_and_deployment():
    """Create a work pool and a test deployment for the Google Reviews pipeline"""
    from prefect.client.schemas.objects import WorkPool

    async with get_client() as client:
        # First, check if we already have a work pool
        work_pools = await client.read_work_pools()
        print(f"Found {len(work_pools)} work pools:")
        for wp in work_pools:
            print(f"- {wp.name} (Type: {wp.type})")

        # Create a work pool if none exists
        work_pool_name = "test-process-pool"
        if not any(wp.name == work_pool_name for wp in work_pools):
            print(f"Creating work pool '{work_pool_name}'...")

            # Use the correct API for creating a work pool
            from prefect.client.schemas.objects import WorkPoolCreate

            work_pool_create = WorkPoolCreate(
                name=work_pool_name,
                type="process",
                description="Test process work pool"
            )

            work_pool = await client.create_work_pool(work_pool=work_pool_create)
            print(f"Created work pool: {work_pool.name} (ID: {work_pool.id})")
        else:
            print(f"Work pool '{work_pool_name}' already exists")

        # Now create a deployment using the serve method
        print("Creating deployment...")

        # We'll use the CLI approach since it's more reliable
        import subprocess
        import os

        # Save the flow to a file
        with open("test_flow.py", "w") as f:
            f.write("""
from prefect import flow, task

@task
def process_data(environment, tenant_key=None, restaurant_key=None):
    print(f"Processing data for environment: {environment}")
    if tenant_key:
        print(f"Tenant key: {tenant_key}")
    if restaurant_key:
        print(f"Restaurant key: {restaurant_key}")
    return {"status": "success", "environment": environment}

@flow(name="Test Google Reviews Pipeline")
def google_reviews_pipeline(environment="DEV", dim_tenant_key=None, dim_restaurant_key=None):
    print(f"Starting Google Reviews pipeline for {environment}")
    result = process_data(environment, dim_tenant_key, dim_restaurant_key)
    print("Pipeline completed successfully")
    return result

if __name__ == "__main__":
    google_reviews_pipeline()
""")

        # Deploy the flow using the CLI
        cmd = f"prefect deploy test_flow.py:google_reviews_pipeline -n 'XDP Google Reviews Pipeline' -p {work_pool_name}"
        print(f"Running: {cmd}")
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print("Deployment created successfully")
            print(result.stdout)
        else:
            print(f"Error creating deployment: {result.stderr}")

        return work_pool_name

async def verify_deployment():
    """Verify the deployment exists"""
    async with get_client() as client:
        deployments = await client.read_deployments()
        print(f"Found {len(deployments)} deployments:")
        for d in deployments:
            print(f"- {d.name} (Flow: {d.flow_name})")

if __name__ == "__main__":
    # Create work pool and deployment
    asyncio.run(create_work_pool_and_deployment())

    # Verify the deployment exists
    asyncio.run(verify_deployment())
