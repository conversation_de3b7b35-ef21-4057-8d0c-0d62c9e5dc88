import subprocess
import os
import json

def run_prefect_command(command):
    """Run a prefect CLI command and return the output"""
    full_command = f"prefect {command}"
    print(f"Running: {full_command}")
    
    # Set environment variables for the subprocess
    env = os.environ.copy()
    env["PREFECT_API_KEY"] = "****************************************"
    
    # Run the command
    result = subprocess.run(
        full_command, 
        shell=True, 
        capture_output=True, 
        text=True,
        env=env
    )
    
    if result.returncode != 0:
        print(f"Error: {result.stderr}")
        return None
    
    return result.stdout

def main():
    # Check Prefect version
    version = run_prefect_command("version")
    print(f"Prefect version: {version}")
    
    # Check current profile
    print("\nChecking current profile...")
    profile = run_prefect_command("profile inspect")
    if profile:
        print(profile)

    # List profiles
    print("\nListing profiles...")
    profiles = run_prefect_command("profile ls")
    if profiles:
        print(profiles)

    # Try to connect to cloud
    print("\nTrying to connect to Prefect Cloud...")
    cloud_login = run_prefect_command("cloud login --key ****************************************")
    if cloud_login:
        print(cloud_login)

    # List deployments
    print("\nListing deployments...")
    deployments = run_prefect_command("deployment ls")
    if deployments:
        print(deployments)

    # List flows
    print("\nListing flows...")
    flows = run_prefect_command("flow ls")
    if flows:
        print(flows)

if __name__ == "__main__":
    main()
