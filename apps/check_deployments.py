import asyncio
from prefect import get_client
import os

async def list_deployments():
    # Method 1: Use environment variables (recommended)
    # Set these before running:
    # export PREFECT_API_KEY="****************************************"
    # export PREFECT_API_URL="https://api.prefect.cloud/api/accounts/YOUR_ACCOUNT_ID/workspaces/YOUR_WORKSPACE_ID"

    # Method 2: Pass directly to get_client()
    api_key = "****************************************"

    # Check if we have environment variables set
    env_api_key = os.environ.get('PREFECT_API_KEY')
    env_api_url = os.environ.get('PREFECT_API_URL')

    print(f"Environment API Key: {'Set' if env_api_key else 'Not set'}")
    print(f"Environment API URL: {env_api_url or 'Not set'}")

    # For Prefect Cloud, we need to set the correct API URL
    # The API key format pnu_* indicates Prefect Cloud
    if api_key.startswith('pnu_'):
        # This is a Prefect Cloud API key
        # Try different API URL formats for Prefect Cloud
        cloud_api_url = 'https://api.prefect.cloud/api'
        os.environ['PREFECT_API_URL'] = cloud_api_url
        print(f"Detected Prefect Cloud API key, setting API URL to: {cloud_api_url}")

        # Also try to disable local server
        os.environ['PREFECT_SERVER_ALLOW_EPHEMERAL_MODE'] = 'false'

    async with get_client() as client:
        print("Connected to Prefect")
        try:
            deployments = await client.read_deployments()
            print('Available deployments:')
            if not deployments:
                print('No deployments found')
            else:
                for d in deployments:
                    print(f'- Name: {d.name}')
                    print(f'  ID: {d.id}')
                    print(f'  Flow name: {d.flow_name}')
                    print('---')
        except Exception as e:
            print(f"Error reading deployments: {e}")

        # Also try to get workspace info
        try:
            # Get current workspace info
            response = await client._client.get("/me/workspaces")
            workspaces = response.json()
            print(f"\nAvailable workspaces: {len(workspaces)}")
            for ws in workspaces:
                print(f"- {ws['workspace_name']} (ID: {ws['workspace_id']})")
        except Exception as e:
            print(f"Error getting workspace info: {e}")

if __name__ == "__main__":
    asyncio.run(list_deployments())
