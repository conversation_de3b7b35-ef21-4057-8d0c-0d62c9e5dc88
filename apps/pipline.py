from prefect import get_client
import asyncio
import time
import os

def trigger_google_reviews(environment="DEV", tenant_key=None, restaurant_key=None, monitor=True, deployment_name="XDP Google Reviews Pipeline"):
    """Trigger the Google Reviews deployment with custom parameters.

    Args:
        environment (str): 'DEV' or 'PROD'
        tenant_key (str, optional): Specific tenant key to process
        restaurant_key (str, optional): Specific restaurant key to process
        monitor (bool): Whether to monitor the flow run until completion
        deployment_name (str): Name of the deployment to trigger

    Returns:
        str: Flow run ID if successful, None if failed
    """

    async def _trigger():
        print(f"Connecting to Prefect server...")
        print(f"API Key: {'Set' if os.environ.get('PREFECT_API_KEY') else 'Not set'}")
        print(f"API URL: {os.environ.get('PREFECT_API_URL', 'Not set (using default)')}")

        async with get_client() as client:
            # List all deployments first
            deployments = await client.read_deployments()
            print(f"Found {len(deployments)} deployments:")
            for d in deployments:
                print(f"- {d.name} (Flow: {d.flow_name})")

            # Find the deployment
            deployment = next((d for d in deployments if d.name == deployment_name), None)

            if not deployment:
                print(f"Deployment '{deployment_name}' not found")
                return None

            # Build parameters
            params = {"environment": environment}
            if tenant_key:
                params["dim_tenant_key"] = tenant_key
            if restaurant_key:
                params["dim_restaurant_key"] = restaurant_key

            print(f"Creating flow run with parameters: {params}")

            # Create flow run using the simple method
            try:
                flow_run = await client.create_flow_run_from_deployment(
                    deployment_id=deployment.id,
                    parameters=params
                )

                print(f"Triggered flow run: {flow_run.id}")

                if monitor:
                    await _monitor_flow_run(client, flow_run.id)

                return flow_run.id
            except Exception as e:
                print(f"Error creating flow run: {e}")
                return None

    return asyncio.run(_trigger())

async def _monitor_flow_run(client, flow_run_id):
    """Monitor a flow run until completion."""
    print(f"Monitoring flow run {flow_run_id}...")
    
    while True:
        # Get flow run status
        flow_run = await client.read_flow_run(flow_run_id)
        state = flow_run.state
        
        if state.is_completed():
            if state.is_completed():
                print(f"✅ Flow run completed successfully!")
            else:
                print(f"❌ Flow run failed with state: {state.name}")
            break
        elif state.is_running():
            print("🔄 Still running...")
        else:
            print(f"⏳ Current state: {state.name}")
        
        # Wait 1 second before checking again
        await asyncio.sleep(1)

if __name__ == "__main__":
    # Example usage for developers:
    
    # Trigger for specific tenant and restaurant in DEV
    trigger_google_reviews(
        environment="DEV",
        tenant_key="f8e3699e-f770-4842-b9aa-82bb706cb558",
        restaurant_key="04e83bf9-0e38-41ae-afaf-831ce8271c45"
    )


# ****************************************