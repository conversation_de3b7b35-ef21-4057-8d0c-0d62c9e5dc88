"""Add flow run alert policies

Revision ID: 888a0bb0df7b
Revises: b75d279ba985
Create Date: 2022-05-12 20:31:58.658936

"""

import sqlalchemy as sa
from alembic import op

import prefect

# revision identifiers, used by Alembic.
revision = "888a0bb0df7b"
down_revision = "b75d279ba985"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "flow_run_alert_queue",
        sa.Column(
            "id",
            prefect.server.utilities.database.UUID(),
            server_default=sa.text(
                "(\n    (\n        lower(hex(randomblob(4))) \n        || '-' \n       "
                " || lower(hex(randomblob(2))) \n        || '-4' \n        ||"
                " substr(lower(hex(randomblob(2))),2) \n        || '-' \n        ||"
                " substr('89ab',abs(random()) % 4 + 1, 1) \n        ||"
                " substr(lower(hex(randomblob(2))),2) \n        || '-' \n        ||"
                " lower(hex(randomblob(6)))\n    )\n    )"
            ),
            nullable=False,
        ),
        sa.Column(
            "created",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column(
            "updated",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column(
            "flow_run_alert_policy_id",
            prefect.server.utilities.database.UUID(),
            nullable=False,
        ),
        sa.Column(
            "flow_run_state_id",
            prefect.server.utilities.database.UUID(),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_flow_run_alert_queue")),
    )
    op.create_index(
        op.f("ix_flow_run_alert_queue__updated"),
        "flow_run_alert_queue",
        ["updated"],
        unique=False,
    )
    op.create_table(
        "flow_run_alert_policy",
        sa.Column(
            "id",
            prefect.server.utilities.database.UUID(),
            server_default=sa.text(
                "(\n    (\n        lower(hex(randomblob(4))) \n        || '-' \n       "
                " || lower(hex(randomblob(2))) \n        || '-4' \n        ||"
                " substr(lower(hex(randomblob(2))),2) \n        || '-' \n        ||"
                " substr('89ab',abs(random()) % 4 + 1, 1) \n        ||"
                " substr(lower(hex(randomblob(2))),2) \n        || '-' \n        ||"
                " lower(hex(randomblob(6)))\n    )\n    )"
            ),
            nullable=False,
        ),
        sa.Column(
            "created",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column(
            "updated",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("is_active", sa.Boolean(), server_default="1", nullable=False),
        sa.Column(
            "state_names",
            prefect.server.utilities.database.JSON(astext_type=sa.Text()),
            server_default="[]",
            nullable=False,
        ),
        sa.Column(
            "tags",
            prefect.server.utilities.database.JSON(astext_type=sa.Text()),
            server_default="[]",
            nullable=False,
        ),
        sa.Column("message_template", sa.String(), nullable=True),
        sa.Column(
            "block_document_id",
            prefect.server.utilities.database.UUID(),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["block_document_id"],
            ["block_document.id"],
            name=op.f("fk_flow_run_alert_policy__block_document_id__block_document"),
            ondelete="cascade",
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_flow_run_alert")),
    )
    op.create_index(
        op.f("ix_flow_run_alert_policy__name"),
        "flow_run_alert_policy",
        ["name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_flow_run_alert_policy__updated"),
        "flow_run_alert_policy",
        ["updated"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_flow_run_alert_policy__name"),
        table_name="flow_run_alert_policy",
    )
    op.drop_index(
        op.f("ix_flow_run_alert_policy__updated"),
        table_name="flow_run_alert_policy",
    )
    op.drop_table("flow_run_alert_policy")
    op.drop_index(
        op.f("ix_flow_run_alert_queue__updated"),
        table_name="flow_run_alert_queue",
    )
    op.drop_table("flow_run_alert_queue")
    # ### end Alembic commands ###
