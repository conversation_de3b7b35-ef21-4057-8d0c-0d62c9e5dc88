import{d as R,e as D,W as d,g as o,f,bu as I,aI as S,al as T,bt as V,i as p,c as j,a as F,o as Y,j as a,k as u,n as e,a6 as Z,bz as z,d5 as A,d6 as v,cZ as M,d7 as O}from"./index-ei-kaitd.js";import{u as E}from"./usePageTitle-LeBMnqrg.js";const K=R({__name:"WorkPoolQueue",setup(G){const c=D(),l=d("workPoolName"),b=o(()=>[l.value]),r=d("workPoolQueueName"),P=o(()=>[r.value]),k={interval:3e5},i=f(c.workPoolQueues.getWorkPoolQueueByName,[l.value,r.value],k),t=o(()=>i.response),_=f(c.workPools.getWorkPoolByName,[l.value],k),q=o(()=>_.response),w=o(()=>{var n;return((n=q.value)==null?void 0:n.type)==="prefect-agent"}),g=o(()=>t.value?`Your work pool ${t.value.name} is ready to go!`:"Your work queue is ready to go!"),y=o(()=>`prefect ${w.value?"agent":"worker"} start --pool "${l.value}" --work-queue "${r.value}"`),Q=o(()=>`Work queues are scoped to a work pool to allow ${w.value?"agents":"workers"} to pull from groups of queues with different priorities.`),{filter:N}=I({workPoolQueues:{name:P},workPools:{name:b}}),W=o(()=>[{label:"Details",hidden:S.xl},{label:"Upcoming Runs"},{label:"Runs"}]),s=T("tab","Details"),{tabs:B}=V(W,s),h=o(()=>r.value?`Work Pool Queue: ${r.value}`:"Work Pool Queue");return E(h),(n,m)=>{const x=p("p-tabs"),C=p("p-layout-well"),$=p("p-layout-default");return t.value?(Y(),j($,{key:0,class:"work-pool-queue"},{header:a(()=>[u(e(O),{"work-pool-queue":t.value,"work-pool-name":e(l),onUpdate:e(i).refresh},null,8,["work-pool-queue","work-pool-name","onUpdate"])]),default:a(()=>[u(C,{class:"work-pool-queue__body"},{header:a(()=>[u(e(M),{command:y.value,title:g.value,subtitle:Q.value},null,8,["command","title","subtitle"])]),well:a(()=>[u(e(v),{alternate:"","work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),default:a(()=>[u(x,{selected:e(s),"onUpdate:selected":m[0]||(m[0]=U=>Z(s)?s.value=U:null),tabs:e(B)},{details:a(()=>[u(e(v),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),"upcoming-runs":a(()=>[u(e(A),{"work-pool-name":e(l),"work-pool-queue":t.value},null,8,["work-pool-name","work-pool-queue"])]),runs:a(()=>[u(e(z),{filter:e(N),prefix:"runs"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})]),_:1})):F("",!0)}}});export{K as default};
//# sourceMappingURL=WorkPoolQueue-CDIR4Cs7.js.map
