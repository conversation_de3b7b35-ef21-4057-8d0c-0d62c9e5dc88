import{d as c,e as _,W as u,K as k,i as m,c as d,o as i,j as l,k as r,n as e,d3 as w,d4 as f}from"./index-ei-kaitd.js";import{u as P}from"./usePageTitle-LeBMnqrg.js";const N=c({__name:"WorkPoolEdit",async setup(y){let o,a;const s=_(),n=u("workPoolName"),t=([o,a]=k(()=>s.workPools.getWorkPoolByName(n.value)),o=await o,a(),o);return P("Create Work Pool"),(C,W)=>{const p=m("p-layout-default");return i(),d(p,null,{header:l(()=>[r(e(f),{"work-pool":e(t)},null,8,["work-pool"])]),default:l(()=>[r(e(w),{"work-pool":e(t)},null,8,["work-pool"])]),_:1})}}});export{N as default};
//# sourceMappingURL=WorkPoolEdit-DorjaUj7.js.map
