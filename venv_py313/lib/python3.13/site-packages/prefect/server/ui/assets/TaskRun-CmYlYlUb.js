import{d as V,u as A,W as B,e as L,g as s,aI as U,al as j,bt as G,bF as f,bG as J,i,c,a as p,o as k,j as e,k as n,n as a,a6 as P,G as W,by as E,bH as H,bI as K,bJ as _,B as R,bK as O,t as X,I as Z,bL as $,be as q}from"./index-ei-kaitd.js";import{u as z}from"./usePageTitle-LeBMnqrg.js";const tt=V({__name:"TaskRun",setup(M){const g=A(),d=B("taskRunId"),b=L(),m=s(()=>[{label:"Details",hidden:U.xl},{label:"Logs"},{label:"Artifacts"},{label:"Task Inputs"}]),l=j("tab","Logs"),{tabs:I}=G(m,l),w=s(()=>d.value?[d.value]:null),y=f(b.taskRuns.getTaskRun,w,{interval:3e4}),t=s(()=>y.response),r=s(()=>{var u;return(u=t.value)==null?void 0:u.flowRunId}),T=s(()=>r.value?[r.value]:null),h=f(b.flowRuns.getFlowRun,T),v=s(()=>{var u;return(u=t.value)!=null&&u.taskInputs?JSON.stringify(t.value.taskInputs,void 0,2):"{}"});function x(){h.refresh(),g.push(q.flowRun(r.value))}J(t);const D=s(()=>t.value?`Task Run: ${t.value.name}`:"Task Run");return z(D),(u,o)=>{const F=i("p-code-highlight"),S=i("p-tabs"),C=i("p-layout-well");return t.value?(k(),c(C,{key:0,class:"task-run"},{header:e(()=>[n(a($),{"task-run-id":t.value.id,onDelete:x},null,8,["task-run-id"])]),well:e(()=>[n(a(_),{alternate:"","task-run":t.value},null,8,["task-run"])]),default:e(()=>[n(S,{selected:a(l),"onUpdate:selected":o[0]||(o[0]=N=>P(l)?l.value=N:null),tabs:a(I)},W({details:e(()=>[n(a(_),{"task-run":t.value},null,8,["task-run"])]),logs:e(()=>[n(a(K),{"task-run":t.value},null,8,["task-run"])]),artifacts:e(()=>[t.value?(k(),c(a(H),{key:0,"task-run":t.value},null,8,["task-run"])):p("",!0)]),"task-inputs":e(()=>[t.value?(k(),c(a(E),{key:0,"text-to-copy":v.value},{default:e(()=>[n(F,{lang:"json",text:v.value,class:"task-run__inputs"},null,8,["text"])]),_:1},8,["text-to-copy"])):p("",!0)]),_:2},[t.value?{name:"task-inputs-heading",fn:e(()=>[o[1]||(o[1]=R(" Task inputs ")),n(a(O),{title:"Task Inputs"},{default:e(()=>[R(X(a(Z).info.taskInput),1)]),_:1})]),key:"0"}:void 0]),1032,["selected","tabs"])]),_:1})):p("",!0)}}});export{tt as default};
//# sourceMappingURL=TaskRun-CmYlYlUb.js.map
