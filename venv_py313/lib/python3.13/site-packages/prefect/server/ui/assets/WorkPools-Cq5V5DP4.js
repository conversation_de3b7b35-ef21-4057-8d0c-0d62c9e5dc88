import{d as m,e as i,f as k,g as t,i as _,c as a,o,j as n,q as f,a as y,F as P,n as s,c_ as g,c$ as h,k as v,d0 as w}from"./index-ei-kaitd.js";import{u as x}from"./usePageTitle-LeBMnqrg.js";const F=m({__name:"WorkPools",setup(C){const r=i(),c={interval:3e4},e=k(r.workPools.getWorkPools,[{}],c),l=t(()=>e.response??[]),p=t(()=>e.executed&&l.value.length==0),u=t(()=>e.executed);return x("Work Pools"),(b,B)=>{const d=_("p-layout-default");return o(),a(d,{class:"work-pools"},{header:n(()=>[v(s(w))]),default:n(()=>[u.value?(o(),f(P,{key:0},[p.value?(o(),a(s(g),{key:0})):(o(),a(s(h),{key:1,onUpdate:s(e).refresh},null,8,["onUpdate"]))],64)):y("",!0)]),_:1})}}});export{F as default};
//# sourceMappingURL=WorkPools-Cq5V5DP4.js.map
