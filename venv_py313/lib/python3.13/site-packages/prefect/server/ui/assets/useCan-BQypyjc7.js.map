{"version": 3, "file": "useCan-BQypyjc7.js", "sources": ["../../src/utilities/permissions.ts", "../../src/compositions/useCan.ts"], "sourcesContent": ["import { Can, WorkspacePermission, WorkspaceFeatureFlag } from '@prefecthq/prefect-ui-library'\nimport { InjectionKey } from 'vue'\n\nconst featureFlags = [\n  'access:workers',\n  'access:artifacts',\n] as const\n\nexport type FeatureFlag = typeof featureFlags[number] | WorkspaceFeatureFlag\n\nexport type Permission = FeatureFlag | WorkspacePermission\n\nexport const canKey: InjectionKey<Can<Permission>> = Symbol('canInjectionKey')\n", "import { Can, inject } from '@prefecthq/prefect-ui-library'\nimport { Permission, canKey } from '@/utilities/permissions'\n\nexport function useCan(): Can<Permission> {\n  return inject(canKey)\n}\n"], "names": ["can<PERSON>ey", "useCan", "inject"], "mappings": "yCAYa,MAAAA,EAAwC,OAAO,iBAAiB,ECTtE,SAASC,GAA0B,CACxC,OAAOC,EAAOF,CAAM,CACtB"}