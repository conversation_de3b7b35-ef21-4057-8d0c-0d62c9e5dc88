import{d as R,bP as C,g as t,aR as B,aS as N,b,q as T,o as a,l as j,k as n,n as e,bQ as D,aW as E,e as V,W,u as $,al as O,f as P,bu as U,bR as q,i as F,c,j as o,a as d,a6 as z,bz as M,bS as Q,bT as Z,bU as A,be as G}from"./index-ei-kaitd.js";import{s as H}from"./index-B4HswuBc.js";import{u as J}from"./usePageTitle-LeBMnqrg.js";const K={class:"flow-stats"},L={class:"flow-stats__cards"},X=R({__name:"FlowStats",props:{flowId:{}},setup(w){const f=w,r={range:{type:"span",seconds:-604800}},{flowId:u}=C(f),i=t(()=>({flowId:u.value,range:r.range}));B(N,{interval:H(30)});const l=t(()=>b.map("FlowStatsFilter",i.value,"FlowRunsFilter")),p=t(()=>b.map("FlowStatsFilter",i.value,"TaskRunsFilter"));return(_,m)=>(a(),T("div",K,[j("div",L,[n(e(D),{filter:l.value},null,8,["filter"]),n(e(E),{filter:p.value},null,8,["filter"])])]))}}),le=R({__name:"Flow",setup(w){const f=V(),r=W("flowId"),u=t(()=>[r.value]),i=$(),l=O("tab","Runs"),p=["Runs","Deployments","Details"],_={interval:3e5},m=P(f.flows.getFlow,[r.value],_),s=t(()=>m.response),{filter:y}=U({flows:{id:u}}),{filter:k}=q({flows:{id:u}});function S(){i.push(G.flows())}const g=t(()=>s.value?`Flow: ${s.value.name}`:"Flow");return J(g),(Y,v)=>{const x=F("p-tabs"),I=F("p-layout-default");return a(),c(I,{class:"flow"},{header:o(()=>[s.value?(a(),c(e(A),{key:0,flow:s.value,onDelete:S},null,8,["flow"])):d("",!0)]),default:o(()=>[s.value?(a(),c(X,{key:0,"flow-id":s.value.id},null,8,["flow-id"])):d("",!0),n(x,{selected:e(l),"onUpdate:selected":v[0]||(v[0]=h=>z(l)?l.value=h:null),tabs:p},{details:o(()=>[s.value?(a(),c(e(Z),{key:0,flow:s.value},null,8,["flow"])):d("",!0)]),deployments:o(()=>[n(e(Q),{filter:e(k),prefix:"deployments"},null,8,["filter"])]),runs:o(()=>[n(e(M),{filter:e(y),selectable:"",prefix:"runs"},null,8,["filter"])]),_:1},8,["selected"])]),_:1})}}});export{le as default};
//# sourceMappingURL=Flow-BUcaLFH0.js.map
