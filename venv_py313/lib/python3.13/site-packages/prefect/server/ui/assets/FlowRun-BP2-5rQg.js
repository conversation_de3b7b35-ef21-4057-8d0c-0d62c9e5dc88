import{d as j,e as P,ae as m,g as c,q as A,o as f,l as R,c as p,a as d,k as a,n as l,bk as D,bl as L,bm as N,bn as S,bo as $,bp as q,bq as W,aw as G,u as J,W as M,br as Q,ah as h,bs as Z,al as H,bt as K,bu as O,bv as X,bw as Y,bx as ee,be as U,i as y,j as o,a6 as le,by as F,bz as te,bA as se,bB as ne,bC as ae,bD as oe,bE as ue}from"./index-ei-kaitd.js";import{u as re}from"./usePageTitle-LeBMnqrg.js";const ie={class:"flow-run-graphs__graph-panel-container"},fe={class:"flow-run-graphs__graphs"},ce={class:"flow-run-graphs__panel p-background"},pe=j({__name:"FlowRunGraphs",props:{flowRun:{}},setup(V){const _=P(),g=m(),s=m(!1),e=m(null),k=c(()=>{var r,t;return{root:{"flow-run-graphs--fullscreen":s.value,"flow-run-graphs--show-panel":((r=e.value)==null?void 0:r.kind)==="task-run"||((t=e.value)==null?void 0:t.kind)==="flow-run"}}}),v=async({nodeId:r,since:t,until:i})=>{const b={anyResource:{id:[`prefect.flow-run.${r}`]},event:{excludePrefix:["prefect.log.write","prefect.task-run."]},occurred:{since:t,until:i}},{events:w}=await _.events.getEvents(b);return w};return(r,t)=>{var i,b,w;return f(),A("div",{class:G(["flow-run-graphs",k.value.root])},[R("div",ie,[R("div",fe,[a(l(D),{fullscreen:s.value,"onUpdate:fullscreen":t[0]||(t[0]=n=>s.value=n),viewport:g.value,"onUpdate:viewport":t[1]||(t[1]=n=>g.value=n),selected:e.value,"onUpdate:selected":t[2]||(t[2]=n=>e.value=n),"flow-run":r.flowRun,"fetch-events":v,class:"flow-run-graphs__flow-run"},null,8,["fullscreen","viewport","selected","flow-run"])]),R("div",ce,[((i=e.value)==null?void 0:i.kind)==="task-run"||((b=e.value)==null?void 0:b.kind)==="flow-run"?(f(),p(l(L),{key:0,selection:e.value,"onUpdate:selection":t[3]||(t[3]=n=>e.value=n),floating:s.value},null,8,["selection","floating"])):d("",!0)])]),e.value&&e.value.kind==="event"?(f(),p(l(N),{key:0,selection:e.value,"onUpdate:selection":t[4]||(t[4]=n=>e.value=n)},null,8,["selection"])):d("",!0),e.value&&e.value.kind==="events"?(f(),p(l(S),{key:1,selection:e.value,"onUpdate:selection":t[5]||(t[5]=n=>e.value=n)},null,8,["selection"])):d("",!0),e.value&&e.value.kind==="artifacts"?(f(),p(l($),{key:2,selection:e.value,"onUpdate:selection":t[6]||(t[6]=n=>e.value=n)},null,8,["selection"])):d("",!0),((w=e.value)==null?void 0:w.kind)==="state"?(f(),p(l(q),{key:3,selection:e.value,"onUpdate:selection":t[7]||(t[7]=n=>e.value=n)},null,8,["selection"])):d("",!0),a(l(W),{selection:e.value,"onUpdate:selection":t[8]||(t[8]=n=>e.value=n)},null,8,["selection"])],2)}}}),be=j({__name:"FlowRun",setup(V){const _=J(),g=M("flowRunId"),{flowRun:s,subscription:e}=Q(g,{interval:5e3}),k=c(()=>{var u;return h(((u=s.value)==null?void 0:u.parameters)??{})}),v=c(()=>{var u;return(u=s.value)!=null&&u.stateType?Z(s.value.stateType):!0}),r=c(()=>{var u;return h(((u=s.value)==null?void 0:u.jobVariables)??{})}),t=c(()=>[{label:"Logs"},{label:"Task Runs",hidden:v.value},{label:"Subflow Runs",hidden:v.value},{label:"Artifacts",hidden:v.value},{label:"Details"},{label:"Parameters"},{label:"Job Variables"}]),i=H("tab","Logs"),{tabs:b}=K(t,i),w=c(()=>[g.value]),{filter:n}=O({flowRuns:{parentFlowRunId:w}});function C(){_.push(U.runs())}X(s);const I=c(()=>s.value?`Flow Run: ${s.value.name}`:"Flow Run");return re(I),Y(()=>{e.error&&ee(e.error).isInRange("clientError")&&_.replace(U[404]())}),(u,x)=>{const E=y("p-code-highlight"),B=y("p-tabs"),T=y("p-layout-default");return l(s)?(f(),p(T,{key:l(s).id,class:"flow-run"},{header:o(()=>[a(l(ue),{"flow-run-id":l(s).id,onDelete:C},null,8,["flow-run-id"])]),default:o(()=>[v.value?d("",!0):(f(),p(pe,{key:0,"flow-run":l(s)},null,8,["flow-run"])),a(B,{selected:l(i),"onUpdate:selected":x[0]||(x[0]=z=>le(i)?i.value=z:null),tabs:l(b)},{details:o(()=>[a(l(oe),{"flow-run":l(s)},null,8,["flow-run"])]),logs:o(()=>[a(l(ae),{"flow-run":l(s)},null,8,["flow-run"])]),artifacts:o(()=>[a(l(ne),{"flow-run":l(s)},null,8,["flow-run"])]),"task-runs":o(()=>[a(l(se),{"flow-run-id":l(s).id},null,8,["flow-run-id"])]),"subflow-runs":o(()=>[a(l(te),{filter:l(n)},null,8,["filter"])]),parameters:o(()=>[a(l(F),{"text-to-copy":k.value},{default:o(()=>[a(E,{lang:"json",text:k.value,class:"flow-run__parameters"},null,8,["text"])]),_:1},8,["text-to-copy"])]),"job-variables":o(()=>[a(l(F),{"text-to-copy":r.value},{default:o(()=>[a(E,{lang:"json",text:r.value,class:"flow-run__job-variables"},null,8,["text"])]),_:1},8,["text-to-copy"])]),_:1},8,["selected","tabs"])]),_:1})):d("",!0)}}});export{be as default};
//# sourceMappingURL=FlowRun-BP2-5rQg.js.map
