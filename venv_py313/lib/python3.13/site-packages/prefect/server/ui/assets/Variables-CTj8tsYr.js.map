{"version": 3, "file": "Variables-CTj8tsYr.js", "sources": ["../../src/pages/Variables.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"variables\">\n    <template #header>\n      <PageHeadingVariables @create=\"refresh\" />\n    </template>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <VariablesPageEmptyState @create=\"refresh\" />\n      </template>\n      <template v-else>\n        <VariablesTable ref=\"table\" @delete=\"refresh\" @update=\"refresh\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { localization, PageHeadingVariables, VariablesTable, VariablesPageEmptyState, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { ref, computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const table = ref<typeof VariablesTable>()\n  const refresh = (): void => {\n    variablesSubscription.value.refresh()\n    table.value?.refreshSubscriptions()\n  }\n  const api = useWorkspaceApi()\n\n  const variablesSubscription = computed(() => useSubscription(api.variables.getVariables))\n  const empty = computed(() => variablesSubscription.value.executed && variablesSubscription.value.response?.length === 0)\n  const loaded = computed(() => variablesSubscription.value.executed)\n  usePageTitle(localization.info.variables)\n</script>"], "names": ["table", "ref", "refresh", "variablesSubscription", "_a", "api", "useWorkspaceApi", "computed", "useSubscription", "empty", "loaded", "usePageTitle", "localization", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingVariables", "_createElementBlock", "_Fragment", "VariablesPageEmptyState", "VariablesTable"], "mappings": "+OAsBE,MAAMA,EAAQC,EAA2B,EACnCC,EAAU,IAAY,OAC1BC,EAAsB,MAAM,QAAQ,GACpCC,EAAAJ,EAAM,QAAN,MAAAI,EAAa,sBACf,EACMC,EAAMC,EAAgB,EAEtBH,EAAwBI,EAAS,IAAMC,EAAgBH,EAAI,UAAU,YAAY,CAAC,EAClFI,EAAQF,EAAS,IAAM,OAAA,OAAAJ,EAAsB,MAAM,YAAYC,EAAAD,EAAsB,MAAM,WAA5B,YAAAC,EAAsC,UAAW,EAAC,EACjHM,EAASH,EAAS,IAAMJ,EAAsB,MAAM,QAAQ,EACrD,OAAAQ,EAAAC,EAAa,KAAK,SAAS,iDA/BxC,EAAAC,EAYmBC,EAAA,CAZD,MAAM,aAAW,CACtB,SACT,IAA0C,CAA1CC,EAA0CC,EAAAC,CAAA,EAAA,CAAnB,SAAQf,CAAO,CAAA,CAAA,aAExC,IAOW,CAPKQ,EAAM,WAAtBQ,EAOWC,EAAA,CAAA,IAAA,GAAA,CANOV,EAAK,WACnBI,EAA6CG,EAAAI,CAAA,EAAA,OAAnB,SAAQlB,UAGlCW,EAAkEG,EAAAK,CAAA,EAAA,eAA9C,QAAJ,IAAIrB,EAAS,SAAQE,EAAU,SAAQA,CAAA"}