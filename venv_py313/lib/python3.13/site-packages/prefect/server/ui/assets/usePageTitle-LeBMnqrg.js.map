{"version": 3, "file": "usePageTitle-LeBMnqrg.js", "sources": ["../../src/compositions/usePageTitle.ts"], "sourcesContent": ["import { computed, Ref, unref, watchEffect } from 'vue'\n\nexport function usePageTitle(...pages: (string | Ref<string | null>)[]): void {\n\n  const pagesWithProject = [...pages, 'Prefect Server']\n\n  const title = computed<string>(() => {\n    return pagesWithProject\n      .map(page => unref(page))\n      .filter(page => page !== null)\n      .join(' • ')\n  })\n\n  watchEffect(() => document.title = title.value)\n}"], "names": ["usePageTitle", "pages", "pagesWithProject", "title", "computed", "page", "unref", "watchEffect"], "mappings": "uDAEO,SAASA,KAAgBC,EAA8C,CAE5E,MAAMC,EAAmB,CAAC,GAAGD,EAAO,gBAAgB,EAE9CE,EAAQC,EAAiB,IACtBF,EACJ,IAAYG,GAAAC,EAAMD,CAAI,CAAC,EACvB,OAAeA,GAAAA,IAAS,IAAI,EAC5B,KAAK,KAAK,CACd,EAEDE,EAAY,IAAM,SAAS,MAAQJ,EAAM,KAAK,CAChD"}