{"version": 3, "file": "RunsPageWithDefaultFilter-BpHg3lPU-B2ixWUwH.js", "sources": ["../../node_modules/@prefecthq/prefect-ui-library/dist/RunsPageWithDefaultFilter-BpHg3lPU.mjs"], "sourcesContent": ["import { defineComponent as u, shallowRef as c, watch as l, createBlock as s, createCommentVNode as m, openBlock as i, resolveDynamicComponent as p } from \"vue\";\nimport { i as f, u as d, a as v, m as y } from \"./index-DbGoIQNu.mjs\";\nconst a = (n) => {\n  const { value: e, isCustom: t } = d();\n  if (v(n.query) && t.value)\n    try {\n      const o = y.map(\"SavedSearchFilter\", e.value, \"LocationQuery\");\n      return { ...n, query: o };\n    } catch (o) {\n      console.error(o);\n    }\n  return !0;\n}, C = /* @__PURE__ */ u({\n  beforeRouteEnter: a,\n  beforeRouteUpdate: a,\n  __name: \"RunsPageWithDefaultFilter\",\n  props: {\n    component: { type: Function }\n  },\n  setup(n) {\n    const e = n, t = c(null);\n    function o(r) {\n      return f(r);\n    }\n    return l(e.component, () => {\n      o(e.component) ? e.component().then((r) => {\n        t.value = r.default;\n      }) : t.value = e.component;\n    }, { immediate: !0 }), (r, h) => t.value !== null ? (i(), s(p(t.value), { key: 0 })) : m(\"\", !0);\n  }\n});\nexport {\n  C as default\n};\n//# sourceMappingURL=RunsPageWithDefaultFilter-BpHg3lPU.mjs.map\n"], "names": ["a", "n", "d", "v", "y", "C", "u", "c", "r", "f", "l", "h", "i", "s", "p", "m"], "mappings": "+FAEK,MAACA,EAAKC,GAAM,CACf,KAAM,CAAE,MAAO,EAAG,SAAU,CAAC,EAAKC,EAAG,EACrC,GAAIC,EAAEF,EAAE,KAAK,GAAK,EAAE,MAClB,GAAI,CACF,MAAM,EAAIG,EAAE,IAAI,oBAAqB,EAAE,MAAO,eAAe,EAC7D,MAAO,CAAE,GAAGH,EAAG,MAAO,CAAG,CAC1B,OAAQ,EAAG,CACV,QAAQ,MAAM,CAAC,CACrB,CACE,MAAO,EACT,EAAGI,EAAoBC,EAAE,CACvB,iBAAkBN,EAClB,kBAAmBA,EACnB,OAAQ,4BACR,MAAO,CACL,UAAW,CAAE,KAAM,QAAQ,CAC5B,EACD,MAAMC,EAAG,CACP,MAAM,EAAIA,EAAG,EAAIM,EAAE,IAAI,EACvB,SAAS,EAAEC,EAAG,CACZ,OAAOC,EAAED,CAAC,CAChB,CACI,OAAOE,EAAE,EAAE,UAAW,IAAM,CAC1B,EAAE,EAAE,SAAS,EAAI,EAAE,YAAY,KAAMF,GAAM,CACzC,EAAE,MAAQA,EAAE,OACb,CAAA,EAAI,EAAE,MAAQ,EAAE,SACvB,EAAO,CAAE,UAAW,EAAE,CAAE,EAAG,CAACA,EAAGG,IAAM,EAAE,QAAU,MAAQC,EAAC,EAAIC,EAAEC,EAAE,EAAE,KAAK,EAAG,CAAE,IAAK,CAAC,CAAE,GAAKC,EAAE,GAAI,EAAE,CACnG,CACA,CAAC", "x_google_ignoreList": [0]}