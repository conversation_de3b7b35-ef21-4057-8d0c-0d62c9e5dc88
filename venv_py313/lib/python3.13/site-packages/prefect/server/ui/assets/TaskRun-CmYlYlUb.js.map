{"version": 3, "file": "TaskRun-CmYlYlUb.js", "sources": ["../../src/pages/TaskRun.vue"], "sourcesContent": ["<template>\n  <p-layout-well v-if=\"taskRun\" class=\"task-run\">\n    <template #header>\n      <PageHeadingTaskRun :task-run-id=\"taskRun.id\" @delete=\"goToFlowRun\" />\n    </template>\n\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <TaskRunDetails :task-run=\"taskRun\" />\n      </template>\n\n      <template #logs>\n        <TaskRunLogs :task-run=\"taskRun\" />\n      </template>\n\n      <template #artifacts>\n        <TaskRunArtifacts v-if=\"taskRun\" :task-run=\"taskRun\" />\n      </template>\n\n      <template v-if=\"taskRun\" #task-inputs-heading>\n        Task inputs\n        <ExtraInfoModal title=\"Task Inputs\">\n          {{ localization.info.taskInput }}\n        </ExtraInfoModal>\n      </template>\n      <template #task-inputs>\n        <CopyableWrapper v-if=\"taskRun\" :text-to-copy=\"parameters\">\n          <p-code-highlight lang=\"json\" :text=\"parameters\" class=\"task-run__inputs\" />\n        </CopyableWrapper>\n      </template>\n    </p-tabs>\n    <template #well>\n      <TaskRunDetails alternate :task-run=\"taskRun\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import { PageHeadingTaskRun, TaskRunArtifacts, TaskRunLogs, TaskRunDetails, CopyableWrapper, useWorkspaceApi, localization, ExtraInfoModal, useTabs, useTaskRunFavicon } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscriptionWithDependencies } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router'\n\n  const router = useRouter()\n  const taskRunId = useRouteParam('taskRunId')\n  const api = useWorkspaceApi()\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Logs' },\n    { label: 'Artifacts' },\n    { label: 'Task Inputs' },\n  ])\n  const tab = useRouteQueryParam('tab', 'Logs')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const taskRunIdArgs = computed<[string] | null>(() => taskRunId.value ? [taskRunId.value] : null)\n  const taskRunDetailsSubscription = useSubscriptionWithDependencies(api.taskRuns.getTaskRun, taskRunIdArgs, { interval: 30000 })\n  const taskRun = computed(() => taskRunDetailsSubscription.response)\n\n  const flowRunId = computed(() => taskRun.value?.flowRunId)\n  const flowRunIdArgs = computed<[string] | null>(() => flowRunId.value ? [flowRunId.value] : null)\n  const flowRunDetailsSubscription = useSubscriptionWithDependencies(api.flowRuns.getFlowRun, flowRunIdArgs)\n\n  const parameters = computed(() => {\n    return taskRun.value?.taskInputs ? JSON.stringify(taskRun.value.taskInputs, undefined, 2) : '{}'\n  })\n\n  function goToFlowRun(): void {\n    flowRunDetailsSubscription.refresh()\n    router.push(routes.flowRun(flowRunId.value!))\n  }\n\n  useTaskRunFavicon(taskRun)\n\n  const title = computed(() => {\n    if (!taskRun.value) {\n      return 'Task Run'\n    }\n    return `Task Run: ${taskRun.value.name}`\n  })\n  usePageTitle(title)\n</script>\n\n<style>\n.task-run__inputs { @apply\n  px-4\n  py-3\n}\n</style>"], "names": ["router", "useRouter", "taskRunId", "useRouteParam", "api", "useWorkspaceApi", "computedTabs", "computed", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "taskRunIdArgs", "taskRunDetailsSubscription", "useSubscriptionWithDependencies", "taskRun", "flowRunId", "_a", "flowRunIdArgs", "flowRunDetailsSubscription", "parameters", "goToFlowRun", "routes", "useTaskRunFavicon", "title", "usePageTitle", "_createBlock", "_component_p_layout_well", "_createVNode", "_unref", "PageHeadingTaskRun", "TaskRunDetails", "_component_p_tabs", "$event", "TaskRunLogs", "TaskRunArtifacts", "CopyableWrapper", "_component_p_code_highlight", "ExtraInfoModal", "localization"], "mappings": "oUA8CE,MAAMA,EAASC,EAAU,EACnBC,EAAYC,EAAc,WAAW,EACrCC,EAAMC,EAAgB,EAEtBC,EAAeC,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQC,EAAM,EAAG,EACrC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,WAAY,EACrB,CAAE,MAAO,aAAc,CAAA,CACxB,EACKC,EAAMC,EAAmB,MAAO,MAAM,EACtC,CAAE,KAAAC,CAAS,EAAAC,EAAQN,EAAcG,CAAG,EAEpCI,EAAgBN,EAA0B,IAAML,EAAU,MAAQ,CAACA,EAAU,KAAK,EAAI,IAAI,EAC1FY,EAA6BC,EAAgCX,EAAI,SAAS,WAAYS,EAAe,CAAE,SAAU,IAAO,EACxHG,EAAUT,EAAS,IAAMO,EAA2B,QAAQ,EAE5DG,EAAYV,EAAS,IAAM,OAAA,OAAAW,EAAAF,EAAQ,QAAR,YAAAE,EAAe,UAAS,EACnDC,EAAgBZ,EAA0B,IAAMU,EAAU,MAAQ,CAACA,EAAU,KAAK,EAAI,IAAI,EAC1FG,EAA6BL,EAAgCX,EAAI,SAAS,WAAYe,CAAa,EAEnGE,EAAad,EAAS,IAAM,OACzB,OAAAW,EAAAF,EAAQ,QAAR,MAAAE,EAAe,WAAa,KAAK,UAAUF,EAAQ,MAAM,WAAY,OAAW,CAAC,EAAI,IAAA,CAC7F,EAED,SAASM,GAAoB,CAC3BF,EAA2B,QAAQ,EACnCpB,EAAO,KAAKuB,EAAO,QAAQN,EAAU,KAAM,CAAC,CAAA,CAG9CO,EAAkBR,CAAO,EAEnB,MAAAS,EAAQlB,EAAS,IAChBS,EAAQ,MAGN,aAAaA,EAAQ,MAAM,IAAI,GAF7B,UAGV,EACD,OAAAU,EAAaD,CAAK,kFAnFGT,EAAO,WAA5BW,EAiCgBC,EAAA,OAjCc,MAAM,UAAA,GACvB,SACT,IAAsE,CAAtEC,EAAsEC,EAAAC,CAAA,EAAA,CAAjD,cAAaf,EAAO,MAAC,GAAK,SAAQM,CAAA,4BA4B9C,OACT,IAAgD,CAAhDO,EAAgDC,EAAAE,CAAA,EAAA,CAAhC,UAAA,GAAW,WAAUhB,EAAO,KAAA,mCA1B9C,IAwBS,CAxBTa,EAwBSI,EAAA,CAxBO,SAAUH,EAAGrB,CAAA,0CAAHA,EAAG,MAAAyB,EAAA,MAAG,KAAMJ,EAAInB,CAAA,MAC7B,UACT,IAAsC,CAAtCkB,EAAsCC,EAAAE,CAAA,EAAA,CAArB,WAAUhB,EAAO,OAAA,KAAA,EAAA,CAAA,UAAA,CAAA,CAAA,GAGzB,OACT,IAAmC,CAAnCa,EAAmCC,EAAAK,CAAA,EAAA,CAArB,WAAUnB,EAAO,OAAA,KAAA,EAAA,CAAA,UAAA,CAAA,CAAA,GAGtB,YACT,IAAuD,CAA/BA,EAAO,WAA/BW,EAAuDG,EAAAM,CAAA,EAAA,OAArB,WAAUpB,EAAO,KAAA,mCAS1C,gBACT,IAEkB,CAFKA,EAAO,WAA9BW,EAEkBG,EAAAO,CAAA,EAAA,OAFe,eAAchB,EAAU,KAAA,aACvD,IAA4E,CAA5EQ,EAA4ES,EAAA,CAA1D,KAAK,OAAQ,KAAMjB,EAAU,MAAE,MAAM,kBAAA,gEAR3CL,EAAO,YAAG,2BAAoB,IAE5C,eAF4C,eAE5C,GAAAa,EAEiBC,EAAAS,CAAA,EAAA,CAFD,MAAM,eAAa,WACjC,IAAiC,KAA9BT,EAAYU,CAAA,EAAC,KAAK,SAAS,EAAA,CAAA,CAAA"}