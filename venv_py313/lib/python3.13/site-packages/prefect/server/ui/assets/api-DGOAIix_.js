var at=Object.defineProperty;var ct=(e,t,n)=>t in e?at(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var N=(e,t,n)=>ct(e,typeof t!="symbol"?t+"":t,n);import{cI as ut,cJ as Le,d as lt,q as ft,o as dt,k as we,l as be,j as ge,B as Re,n as Ee,cK as pt,cL as ht,ae as mt,L as yt,cM as wt,cN as bt,cO as gt,cP as ne,cQ as Rt}from"./index-ei-kaitd.js";import{m as F}from"./mapper-BuxGYc8V.js";function De(e,t){return function(){return e.apply(t,arguments)}}const{toString:Et}=Object.prototype,{getPrototypeOf:pe}=Object,G=(e=>t=>{const n=Et.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),x=e=>(e=e.toLowerCase(),t=>G(t)===e),Q=e=>t=>typeof t===e,{isArray:q}=Array,M=Q("undefined");function St(e){return e!==null&&!M(e)&&e.constructor!==null&&!M(e.constructor)&&C(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const je=x("ArrayBuffer");function Tt(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&je(e.buffer),t}const At=Q("string"),C=Q("function"),qe=Q("number"),Z=e=>e!==null&&typeof e=="object",Ot=e=>e===!0||e===!1,J=e=>{if(G(e)!=="object")return!1;const t=pe(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Ct=x("Date"),xt=x("File"),Pt=x("Blob"),kt=x("FileList"),Nt=e=>Z(e)&&C(e.pipe),_t=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||C(e.append)&&((t=G(e))==="formdata"||t==="object"&&C(e.toString)&&e.toString()==="[object FormData]"))},Ft=x("URLSearchParams"),[Ut,Bt,Lt,Dt]=["ReadableStream","Request","Response","Headers"].map(x),jt=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function $(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let r,s;if(typeof e!="object"&&(e=[e]),q(e))for(r=0,s=e.length;r<s;r++)t.call(null,e[r],r,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let c;for(r=0;r<i;r++)c=o[r],t.call(null,e[c],c,e)}}function Ie(e,t){t=t.toLowerCase();const n=Object.keys(e);let r=n.length,s;for(;r-- >0;)if(s=n[r],t===s.toLowerCase())return s;return null}const B=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,He=e=>!M(e)&&e!==B;function ae(){const{caseless:e}=He(this)&&this||{},t={},n=(r,s)=>{const o=e&&Ie(t,s)||s;J(t[o])&&J(r)?t[o]=ae(t[o],r):J(r)?t[o]=ae({},r):q(r)?t[o]=r.slice():t[o]=r};for(let r=0,s=arguments.length;r<s;r++)arguments[r]&&$(arguments[r],n);return t}const qt=(e,t,n,{allOwnKeys:r}={})=>($(t,(s,o)=>{n&&C(s)?e[o]=De(s,n):e[o]=s},{allOwnKeys:r}),e),It=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),Ht=(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},Mt=(e,t,n,r)=>{let s,o,i;const c={};if(t=t||{},e==null)return t;do{for(s=Object.getOwnPropertyNames(e),o=s.length;o-- >0;)i=s[o],(!r||r(i,e,t))&&!c[i]&&(t[i]=e[i],c[i]=!0);e=n!==!1&&pe(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},$t=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return r!==-1&&r===n},zt=e=>{if(!e)return null;if(q(e))return e;let t=e.length;if(!qe(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},vt=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&pe(Uint8Array)),Jt=(e,t)=>{const r=(e&&e[Symbol.iterator]).call(e);let s;for(;(s=r.next())&&!s.done;){const o=s.value;t.call(e,o[0],o[1])}},Vt=(e,t)=>{let n;const r=[];for(;(n=e.exec(t))!==null;)r.push(n);return r},Kt=x("HTMLFormElement"),Wt=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,r,s){return r.toUpperCase()+s}),Se=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Xt=x("RegExp"),Me=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};$(n,(s,o)=>{let i;(i=t(s,o,e))!==!1&&(r[o]=i||s)}),Object.defineProperties(e,r)},Gt=e=>{Me(e,(t,n)=>{if(C(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const r=e[n];if(C(r)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Qt=(e,t)=>{const n={},r=s=>{s.forEach(o=>{n[o]=!0})};return q(e)?r(e):r(String(e).split(t)),n},Zt=()=>{},Yt=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function en(e){return!!(e&&C(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const tn=e=>{const t=new Array(10),n=(r,s)=>{if(Z(r)){if(t.indexOf(r)>=0)return;if(!("toJSON"in r)){t[s]=r;const o=q(r)?[]:{};return $(r,(i,c)=>{const f=n(i,s+1);!M(f)&&(o[c]=f)}),t[s]=void 0,o}}return r};return n(e,0)},nn=x("AsyncFunction"),rn=e=>e&&(Z(e)||C(e))&&C(e.then)&&C(e.catch),$e=((e,t)=>e?setImmediate:t?((n,r)=>(B.addEventListener("message",({source:s,data:o})=>{s===B&&o===n&&r.length&&r.shift()()},!1),s=>{r.push(s),B.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",C(B.postMessage)),sn=typeof queueMicrotask<"u"?queueMicrotask.bind(B):typeof process<"u"&&process.nextTick||$e,a={isArray:q,isArrayBuffer:je,isBuffer:St,isFormData:_t,isArrayBufferView:Tt,isString:At,isNumber:qe,isBoolean:Ot,isObject:Z,isPlainObject:J,isReadableStream:Ut,isRequest:Bt,isResponse:Lt,isHeaders:Dt,isUndefined:M,isDate:Ct,isFile:xt,isBlob:Pt,isRegExp:Xt,isFunction:C,isStream:Nt,isURLSearchParams:Ft,isTypedArray:vt,isFileList:kt,forEach:$,merge:ae,extend:qt,trim:jt,stripBOM:It,inherits:Ht,toFlatObject:Mt,kindOf:G,kindOfTest:x,endsWith:$t,toArray:zt,forEachEntry:Jt,matchAll:Vt,isHTMLForm:Kt,hasOwnProperty:Se,hasOwnProp:Se,reduceDescriptors:Me,freezeMethods:Gt,toObjectSet:Qt,toCamelCase:Wt,noop:Zt,toFiniteNumber:Yt,findKey:Ie,global:B,isContextDefined:He,isSpecCompliantForm:en,toJSONObject:tn,isAsyncFn:nn,isThenable:rn,setImmediate:$e,asap:sn};function m(e,t,n,r,s){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),s&&(this.response=s,this.status=s.status?s.status:null)}a.inherits(m,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const ze=m.prototype,ve={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{ve[e]={value:e}});Object.defineProperties(m,ve);Object.defineProperty(ze,"isAxiosError",{value:!0});m.from=(e,t,n,r,s,o)=>{const i=Object.create(ze);return a.toFlatObject(e,i,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),m.call(i,e.message,t,n,r,s),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const on=null;function ce(e){return a.isPlainObject(e)||a.isArray(e)}function Je(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Te(e,t,n){return e?e.concat(t).map(function(s,o){return s=Je(s),!n&&o?"["+s+"]":s}).join(n?".":""):t}function an(e){return a.isArray(e)&&!e.some(ce)}const cn=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function Y(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,h){return!a.isUndefined(h[y])});const r=n.metaTokens,s=n.visitor||l,o=n.dots,i=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(s))throw new TypeError("visitor must be a function");function u(p){if(p===null)return"";if(a.isDate(p))return p.toISOString();if(!f&&a.isBlob(p))throw new m("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(p)||a.isTypedArray(p)?f&&typeof Blob=="function"?new Blob([p]):Buffer.from(p):p}function l(p,y,h){let b=p;if(p&&!h&&typeof p=="object"){if(a.endsWith(y,"{}"))y=r?y:y.slice(0,-2),p=JSON.stringify(p);else if(a.isArray(p)&&an(p)||(a.isFileList(p)||a.endsWith(y,"[]"))&&(b=a.toArray(p)))return y=Je(y),b.forEach(function(S,k){!(a.isUndefined(S)||S===null)&&t.append(i===!0?Te([y],k,o):i===null?y:y+"[]",u(S))}),!1}return ce(p)?!0:(t.append(Te(h,y,o),u(p)),!1)}const d=[],w=Object.assign(cn,{defaultVisitor:l,convertValue:u,isVisitable:ce});function R(p,y){if(!a.isUndefined(p)){if(d.indexOf(p)!==-1)throw Error("Circular reference detected in "+y.join("."));d.push(p),a.forEach(p,function(b,E){(!(a.isUndefined(b)||b===null)&&s.call(t,b,a.isString(E)?E.trim():E,y,w))===!0&&R(b,y?y.concat(E):[E])}),d.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return R(e),t}function Ae(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(r){return t[r]})}function he(e,t){this._pairs=[],e&&Y(e,this,t)}const Ve=he.prototype;Ve.append=function(t,n){this._pairs.push([t,n])};Ve.toString=function(t){const n=t?function(r){return t.call(this,r,Ae)}:Ae;return this._pairs.map(function(s){return n(s[0])+"="+n(s[1])},"").join("&")};function un(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ke(e,t,n){if(!t)return e;const r=n&&n.encode||un;a.isFunction(n)&&(n={serialize:n});const s=n&&n.serialize;let o;if(s?o=s(t,n):o=a.isURLSearchParams(t)?t.toString():new he(t,n).toString(r),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Oe{constructor(){this.handlers=[]}use(t,n,r){return this.handlers.push({fulfilled:t,rejected:n,synchronous:r?r.synchronous:!1,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(r){r!==null&&t(r)})}}const We={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ln=typeof URLSearchParams<"u"?URLSearchParams:he,fn=typeof FormData<"u"?FormData:null,dn=typeof Blob<"u"?Blob:null,pn={isBrowser:!0,classes:{URLSearchParams:ln,FormData:fn,Blob:dn},protocols:["http","https","file","blob","url","data"]},me=typeof window<"u"&&typeof document<"u",ue=typeof navigator=="object"&&navigator||void 0,hn=me&&(!ue||["ReactNative","NativeScript","NS"].indexOf(ue.product)<0),mn=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",yn=me&&window.location.href||"http://localhost",wn=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:me,hasStandardBrowserEnv:hn,hasStandardBrowserWebWorkerEnv:mn,navigator:ue,origin:yn},Symbol.toStringTag,{value:"Module"})),T={...wn,...pn};function bn(e,t){return Y(e,new T.classes.URLSearchParams,Object.assign({visitor:function(n,r,s,o){return T.isNode&&a.isBuffer(n)?(this.append(r,n.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function gn(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Rn(e){const t={},n=Object.keys(e);let r;const s=n.length;let o;for(r=0;r<s;r++)o=n[r],t[o]=e[o];return t}function Xe(e){function t(n,r,s,o){let i=n[o++];if(i==="__proto__")return!0;const c=Number.isFinite(+i),f=o>=n.length;return i=!i&&a.isArray(s)?s.length:i,f?(a.hasOwnProp(s,i)?s[i]=[s[i],r]:s[i]=r,!c):((!s[i]||!a.isObject(s[i]))&&(s[i]=[]),t(n,r,s[i],o)&&a.isArray(s[i])&&(s[i]=Rn(s[i])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(r,s)=>{t(gn(r),s,n,0)}),n}return null}function En(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(r){if(r.name!=="SyntaxError")throw r}return(n||JSON.stringify)(e)}const z={transitional:We,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const r=n.getContentType()||"",s=r.indexOf("application/json")>-1,o=a.isObject(t);if(o&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return s?JSON.stringify(Xe(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(o){if(r.indexOf("application/x-www-form-urlencoded")>-1)return bn(t,this.formSerializer).toString();if((c=a.isFileList(t))||r.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Y(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return o||s?(n.setContentType("application/json",!1),En(t)):t}],transformResponse:[function(t){const n=this.transitional||z.transitional,r=n&&n.forcedJSONParsing,s=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(r&&!this.responseType||s)){const i=!(n&&n.silentJSONParsing)&&s;try{return JSON.parse(t)}catch(c){if(i)throw c.name==="SyntaxError"?m.from(c,m.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:T.classes.FormData,Blob:T.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{z.headers[e]={}});const Sn=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Tn=e=>{const t={};let n,r,s;return e&&e.split(`
`).forEach(function(i){s=i.indexOf(":"),n=i.substring(0,s).trim().toLowerCase(),r=i.substring(s+1).trim(),!(!n||t[n]&&Sn[n])&&(n==="set-cookie"?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)}),t},Ce=Symbol("internals");function H(e){return e&&String(e).trim().toLowerCase()}function V(e){return e===!1||e==null?e:a.isArray(e)?e.map(V):String(e)}function An(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}const On=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function re(e,t,n,r,s){if(a.isFunction(r))return r.call(this,t,n);if(s&&(t=n),!!a.isString(t)){if(a.isString(r))return t.indexOf(r)!==-1;if(a.isRegExp(r))return r.test(t)}}function Cn(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,r)=>n.toUpperCase()+r)}function xn(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(r=>{Object.defineProperty(e,r+n,{value:function(s,o,i){return this[r].call(this,t,s,o,i)},configurable:!0})})}let O=class{constructor(t){t&&this.set(t)}set(t,n,r){const s=this;function o(c,f,u){const l=H(f);if(!l)throw new Error("header name must be a non-empty string");const d=a.findKey(s,l);(!d||s[d]===void 0||u===!0||u===void 0&&s[d]!==!1)&&(s[d||f]=V(c))}const i=(c,f)=>a.forEach(c,(u,l)=>o(u,l,f));if(a.isPlainObject(t)||t instanceof this.constructor)i(t,n);else if(a.isString(t)&&(t=t.trim())&&!On(t))i(Tn(t),n);else if(a.isHeaders(t))for(const[c,f]of t.entries())o(f,c,r);else t!=null&&o(n,t,r);return this}get(t,n){if(t=H(t),t){const r=a.findKey(this,t);if(r){const s=this[r];if(!n)return s;if(n===!0)return An(s);if(a.isFunction(n))return n.call(this,s,r);if(a.isRegExp(n))return n.exec(s);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=H(t),t){const r=a.findKey(this,t);return!!(r&&this[r]!==void 0&&(!n||re(this,this[r],r,n)))}return!1}delete(t,n){const r=this;let s=!1;function o(i){if(i=H(i),i){const c=a.findKey(r,i);c&&(!n||re(r,r[c],c,n))&&(delete r[c],s=!0)}}return a.isArray(t)?t.forEach(o):o(t),s}clear(t){const n=Object.keys(this);let r=n.length,s=!1;for(;r--;){const o=n[r];(!t||re(this,this[o],o,t,!0))&&(delete this[o],s=!0)}return s}normalize(t){const n=this,r={};return a.forEach(this,(s,o)=>{const i=a.findKey(r,o);if(i){n[i]=V(s),delete n[o];return}const c=t?Cn(o):String(o).trim();c!==o&&delete n[o],n[c]=V(s),r[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(r,s)=>{r!=null&&r!==!1&&(n[s]=t&&a.isArray(r)?r.join(", "):r)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const r=new this(t);return n.forEach(s=>r.set(s)),r}static accessor(t){const r=(this[Ce]=this[Ce]={accessors:{}}).accessors,s=this.prototype;function o(i){const c=H(i);r[c]||(xn(s,i),r[c]=!0)}return a.isArray(t)?t.forEach(o):o(t),this}};O.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(O.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(r){this[n]=r}}});a.freezeMethods(O);function se(e,t){const n=this||z,r=t||n,s=O.from(r.headers);let o=r.data;return a.forEach(e,function(c){o=c.call(n,o,s.normalize(),t?t.status:void 0)}),s.normalize(),o}function Ge(e){return!!(e&&e.__CANCEL__)}function I(e,t,n){m.call(this,e??"canceled",m.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(I,m,{__CANCEL__:!0});function Qe(e,t,n){const r=n.config.validateStatus;!n.status||!r||r(n.status)?e(n):t(new m("Request failed with status code "+n.status,[m.ERR_BAD_REQUEST,m.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Pn(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function kn(e,t){e=e||10;const n=new Array(e),r=new Array(e);let s=0,o=0,i;return t=t!==void 0?t:1e3,function(f){const u=Date.now(),l=r[o];i||(i=u),n[s]=f,r[s]=u;let d=o,w=0;for(;d!==s;)w+=n[d++],d=d%e;if(s=(s+1)%e,s===o&&(o=(o+1)%e),u-i<t)return;const R=l&&u-l;return R?Math.round(w*1e3/R):void 0}}function Nn(e,t){let n=0,r=1e3/t,s,o;const i=(u,l=Date.now())=>{n=l,s=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const l=Date.now(),d=l-n;d>=r?i(u,l):(s=u,o||(o=setTimeout(()=>{o=null,i(s)},r-d)))},()=>s&&i(s)]}const W=(e,t,n=3)=>{let r=0;const s=kn(50,250);return Nn(o=>{const i=o.loaded,c=o.lengthComputable?o.total:void 0,f=i-r,u=s(f),l=i<=c;r=i;const d={loaded:i,total:c,progress:c?i/c:void 0,bytes:f,rate:u||void 0,estimated:u&&c&&l?(c-i)/u:void 0,event:o,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(d)},n)},xe=(e,t)=>{const n=e!=null;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Pe=e=>(...t)=>a.asap(()=>e(...t)),_n=T.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,T.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(T.origin),T.navigator&&/(msie|trident)/i.test(T.navigator.userAgent)):()=>!0,Fn=T.hasStandardBrowserEnv?{write(e,t,n,r,s,o){const i=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),a.isString(r)&&i.push("path="+r),a.isString(s)&&i.push("domain="+s),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Un(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Bn(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Ze(e,t,n){let r=!Un(t);return e&&r||n==!1?Bn(e,t):t}const ke=e=>e instanceof O?{...e}:e;function j(e,t){t=t||{};const n={};function r(u,l,d,w){return a.isPlainObject(u)&&a.isPlainObject(l)?a.merge.call({caseless:w},u,l):a.isPlainObject(l)?a.merge({},l):a.isArray(l)?l.slice():l}function s(u,l,d,w){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u,d,w)}else return r(u,l,d,w)}function o(u,l){if(!a.isUndefined(l))return r(void 0,l)}function i(u,l){if(a.isUndefined(l)){if(!a.isUndefined(u))return r(void 0,u)}else return r(void 0,l)}function c(u,l,d){if(d in t)return r(u,l);if(d in e)return r(void 0,u)}const f={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:c,headers:(u,l,d)=>s(ke(u),ke(l),d,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(l){const d=f[l]||s,w=d(e[l],t[l],l);a.isUndefined(w)&&d!==c||(n[l]=w)}),n}const Ye=e=>{const t=j({},e);let{data:n,withXSRFToken:r,xsrfHeaderName:s,xsrfCookieName:o,headers:i,auth:c}=t;t.headers=i=O.from(i),t.url=Ke(Ze(t.baseURL,t.url),e.params,e.paramsSerializer),c&&i.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(T.hasStandardBrowserEnv||T.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((f=i.getContentType())!==!1){const[u,...l]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...l].join("; "))}}if(T.hasStandardBrowserEnv&&(r&&a.isFunction(r)&&(r=r(t)),r||r!==!1&&_n(t.url))){const u=s&&o&&Fn.read(o);u&&i.set(s,u)}return t},Ln=typeof XMLHttpRequest<"u",Dn=Ln&&function(e){return new Promise(function(n,r){const s=Ye(e);let o=s.data;const i=O.from(s.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:u}=s,l,d,w,R,p;function y(){R&&R(),p&&p(),s.cancelToken&&s.cancelToken.unsubscribe(l),s.signal&&s.signal.removeEventListener("abort",l)}let h=new XMLHttpRequest;h.open(s.method.toUpperCase(),s.url,!0),h.timeout=s.timeout;function b(){if(!h)return;const S=O.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),A={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:S,config:e,request:h};Qe(function(U){n(U),y()},function(U){r(U),y()},A),h=null}"onloadend"in h?h.onloadend=b:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(b)},h.onabort=function(){h&&(r(new m("Request aborted",m.ECONNABORTED,e,h)),h=null)},h.onerror=function(){r(new m("Network Error",m.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let k=s.timeout?"timeout of "+s.timeout+"ms exceeded":"timeout exceeded";const A=s.transitional||We;s.timeoutErrorMessage&&(k=s.timeoutErrorMessage),r(new m(k,A.clarifyTimeoutError?m.ETIMEDOUT:m.ECONNABORTED,e,h)),h=null},o===void 0&&i.setContentType(null),"setRequestHeader"in h&&a.forEach(i.toJSON(),function(k,A){h.setRequestHeader(A,k)}),a.isUndefined(s.withCredentials)||(h.withCredentials=!!s.withCredentials),c&&c!=="json"&&(h.responseType=s.responseType),u&&([w,p]=W(u,!0),h.addEventListener("progress",w)),f&&h.upload&&([d,R]=W(f),h.upload.addEventListener("progress",d),h.upload.addEventListener("loadend",R)),(s.cancelToken||s.signal)&&(l=S=>{h&&(r(!S||S.type?new I(null,e,h):S),h.abort(),h=null)},s.cancelToken&&s.cancelToken.subscribe(l),s.signal&&(s.signal.aborted?l():s.signal.addEventListener("abort",l)));const E=Pn(s.url);if(E&&T.protocols.indexOf(E)===-1){r(new m("Unsupported protocol "+E+":",m.ERR_BAD_REQUEST,e));return}h.send(o||null)})},jn=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let r=new AbortController,s;const o=function(u){if(!s){s=!0,c();const l=u instanceof Error?u:this.reason;r.abort(l instanceof m?l:new I(l instanceof Error?l.message:l))}};let i=t&&setTimeout(()=>{i=null,o(new m(`timeout ${t} of ms exceeded`,m.ETIMEDOUT))},t);const c=()=>{e&&(i&&clearTimeout(i),i=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(o):u.removeEventListener("abort",o)}),e=null)};e.forEach(u=>u.addEventListener("abort",o));const{signal:f}=r;return f.unsubscribe=()=>a.asap(c),f}},qn=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let r=0,s;for(;r<n;)s=r+t,yield e.slice(r,s),r=s},In=async function*(e,t){for await(const n of Hn(e))yield*qn(n,t)},Hn=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:r}=await t.read();if(n)break;yield r}}finally{await t.cancel()}},Ne=(e,t,n,r)=>{const s=In(e,t);let o=0,i,c=f=>{i||(i=!0,r&&r(f))};return new ReadableStream({async pull(f){try{const{done:u,value:l}=await s.next();if(u){c(),f.close();return}let d=l.byteLength;if(n){let w=o+=d;n(w)}f.enqueue(new Uint8Array(l))}catch(u){throw c(u),u}},cancel(f){return c(f),s.return()}},{highWaterMark:2})},ee=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",et=ee&&typeof ReadableStream=="function",Mn=ee&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),tt=(e,...t)=>{try{return!!e(...t)}catch{return!1}},$n=et&&tt(()=>{let e=!1;const t=new Request(T.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),_e=64*1024,le=et&&tt(()=>a.isReadableStream(new Response("").body)),X={stream:le&&(e=>e.body)};ee&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!X[t]&&(X[t]=a.isFunction(e[t])?n=>n[t]():(n,r)=>{throw new m(`Response type '${t}' is not supported`,m.ERR_NOT_SUPPORT,r)})})})(new Response);const zn=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(T.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await Mn(e)).byteLength},vn=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??zn(t)},Jn=ee&&(async e=>{let{url:t,method:n,data:r,signal:s,cancelToken:o,timeout:i,onDownloadProgress:c,onUploadProgress:f,responseType:u,headers:l,withCredentials:d="same-origin",fetchOptions:w}=Ye(e);u=u?(u+"").toLowerCase():"text";let R=jn([s,o&&o.toAbortSignal()],i),p;const y=R&&R.unsubscribe&&(()=>{R.unsubscribe()});let h;try{if(f&&$n&&n!=="get"&&n!=="head"&&(h=await vn(l,r))!==0){let A=new Request(t,{method:"POST",body:r,duplex:"half"}),_;if(a.isFormData(r)&&(_=A.headers.get("content-type"))&&l.setContentType(_),A.body){const[U,v]=xe(h,W(Pe(f)));r=Ne(A.body,_e,U,v)}}a.isString(d)||(d=d?"include":"omit");const b="credentials"in Request.prototype;p=new Request(t,{...w,signal:R,method:n.toUpperCase(),headers:l.normalize().toJSON(),body:r,duplex:"half",credentials:b?d:void 0});let E=await fetch(p);const S=le&&(u==="stream"||u==="response");if(le&&(c||S&&y)){const A={};["status","statusText","headers"].forEach(ye=>{A[ye]=E[ye]});const _=a.toFiniteNumber(E.headers.get("content-length")),[U,v]=c&&xe(_,W(Pe(c),!0))||[];E=new Response(Ne(E.body,_e,U,()=>{v&&v(),y&&y()}),A)}u=u||"text";let k=await X[a.findKey(X,u)||"text"](E,e);return!S&&y&&y(),await new Promise((A,_)=>{Qe(A,_,{data:k,headers:O.from(E.headers),status:E.status,statusText:E.statusText,config:e,request:p})})}catch(b){throw y&&y(),b&&b.name==="TypeError"&&/fetch/i.test(b.message)?Object.assign(new m("Network Error",m.ERR_NETWORK,e,p),{cause:b.cause||b}):m.from(b,b&&b.code,e,p)}}),fe={http:on,xhr:Dn,fetch:Jn};a.forEach(fe,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Fe=e=>`- ${e}`,Vn=e=>a.isFunction(e)||e===null||e===!1,nt={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,r;const s={};for(let o=0;o<t;o++){n=e[o];let i;if(r=n,!Vn(n)&&(r=fe[(i=String(n)).toLowerCase()],r===void 0))throw new m(`Unknown adapter '${i}'`);if(r)break;s[i||"#"+o]=r}if(!r){const o=Object.entries(s).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(Fe).join(`
`):" "+Fe(o[0]):"as no adapter specified";throw new m("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return r},adapters:fe};function oe(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new I(null,e)}function Ue(e){return oe(e),e.headers=O.from(e.headers),e.data=se.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),nt.getAdapter(e.adapter||z.adapter)(e).then(function(r){return oe(e),r.data=se.call(e,e.transformResponse,r),r.headers=O.from(r.headers),r},function(r){return Ge(r)||(oe(e),r&&r.response&&(r.response.data=se.call(e,e.transformResponse,r.response),r.response.headers=O.from(r.response.headers))),Promise.reject(r)})}const rt="1.8.2",te={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{te[e]=function(r){return typeof r===e||"a"+(t<1?"n ":" ")+e}});const Be={};te.transitional=function(t,n,r){function s(o,i){return"[Axios v"+rt+"] Transitional option '"+o+"'"+i+(r?". "+r:"")}return(o,i,c)=>{if(t===!1)throw new m(s(i," has been removed"+(n?" in "+n:"")),m.ERR_DEPRECATED);return n&&!Be[i]&&(Be[i]=!0,console.warn(s(i," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(o,i,c):!0}};te.spelling=function(t){return(n,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};function Kn(e,t,n){if(typeof e!="object")throw new m("options must be an object",m.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let s=r.length;for(;s-- >0;){const o=r[s],i=t[o];if(i){const c=e[o],f=c===void 0||i(c,o,e);if(f!==!0)throw new m("option "+o+" must be "+f,m.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new m("Unknown option "+o,m.ERR_BAD_OPTION)}}const K={assertOptions:Kn,validators:te},P=K.validators;let L=class{constructor(t){this.defaults=t,this.interceptors={request:new Oe,response:new Oe}}async request(t,n){try{return await this._request(t,n)}catch(r){if(r instanceof Error){let s={};Error.captureStackTrace?Error.captureStackTrace(s):s=new Error;const o=s.stack?s.stack.replace(/^.+\n/,""):"";try{r.stack?o&&!String(r.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(r.stack+=`
`+o):r.stack=o}catch{}}throw r}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=j(this.defaults,n);const{transitional:r,paramsSerializer:s,headers:o}=n;r!==void 0&&K.assertOptions(r,{silentJSONParsing:P.transitional(P.boolean),forcedJSONParsing:P.transitional(P.boolean),clarifyTimeoutError:P.transitional(P.boolean)},!1),s!=null&&(a.isFunction(s)?n.paramsSerializer={serialize:s}:K.assertOptions(s,{encode:P.function,serialize:P.function},!0)),n.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?n.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:n.allowAbsoluteUrls=!0),K.assertOptions(n,{baseUrl:P.spelling("baseURL"),withXsrfToken:P.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let i=o&&a.merge(o.common,o[n.method]);o&&a.forEach(["delete","get","head","post","put","patch","common"],p=>{delete o[p]}),n.headers=O.concat(i,o);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const u=[];this.interceptors.response.forEach(function(y){u.push(y.fulfilled,y.rejected)});let l,d=0,w;if(!f){const p=[Ue.bind(this),void 0];for(p.unshift.apply(p,c),p.push.apply(p,u),w=p.length,l=Promise.resolve(n);d<w;)l=l.then(p[d++],p[d++]);return l}w=c.length;let R=n;for(d=0;d<w;){const p=c[d++],y=c[d++];try{R=p(R)}catch(h){y.call(this,h);break}}try{l=Ue.call(this,R)}catch(p){return Promise.reject(p)}for(d=0,w=u.length;d<w;)l=l.then(u[d++],u[d++]);return l}getUri(t){t=j(this.defaults,t);const n=Ze(t.baseURL,t.url,t.allowAbsoluteUrls);return Ke(n,t.params,t.paramsSerializer)}};a.forEach(["delete","get","head","options"],function(t){L.prototype[t]=function(n,r){return this.request(j(r||{},{method:t,url:n,data:(r||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(r){return function(o,i,c){return this.request(j(c||{},{method:t,headers:r?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}L.prototype[t]=n(),L.prototype[t+"Form"]=n(!0)});let Wn=class st{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(o){n=o});const r=this;this.promise.then(s=>{if(!r._listeners)return;let o=r._listeners.length;for(;o-- >0;)r._listeners[o](s);r._listeners=null}),this.promise.then=s=>{let o;const i=new Promise(c=>{r.subscribe(c),o=c}).then(s);return i.cancel=function(){r.unsubscribe(o)},i},t(function(o,i,c){r.reason||(r.reason=new I(o,i,c),n(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=r=>{t.abort(r)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new st(function(s){t=s}),cancel:t}}};function Xn(e){return function(n){return e.apply(null,n)}}function Gn(e){return a.isObject(e)&&e.isAxiosError===!0}const de={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(de).forEach(([e,t])=>{de[t]=e});function ot(e){const t=new L(e),n=De(L.prototype.request,t);return a.extend(n,L.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(s){return ot(j(e,s))},n}const g=ot(z);g.Axios=L;g.CanceledError=I;g.CancelToken=Wn;g.isCancel=Ge;g.VERSION=rt;g.toFormData=Y;g.AxiosError=m;g.Cancel=g.CanceledError;g.all=function(t){return Promise.all(t)};g.spread=Xn;g.isAxiosError=Gn;g.mergeConfig=j;g.AxiosHeaders=O;g.formToJSON=e=>Xe(a.isHTMLForm(e)?new FormData(e):e);g.getAdapter=nt.getAdapter;g.HttpStatusCode=de;g.default=g;const{Axios:hr,AxiosError:mr,CanceledError:yr,isCancel:wr,CancelToken:br,VERSION:gr,all:Rr,Cancel:Er,isAxiosError:it,spread:Sr,toFormData:Tr,AxiosHeaders:Ar,HttpStatusCode:Or,formToJSON:Cr,getAdapter:xr,mergeConfig:Pr}=g;class D{static async load(){if(this.settings!==null)return this.settings;if(this.promise!==null)return this.promise;this.promise=new Promise(n=>g.get("/ui-settings",{baseURL:this.baseUrl}).then(({data:r})=>F.map("SettingsResponse",r,"Settings")).then(n));const t=await this.promise;return this.settings=t}static async get(t,n){var s;await this.load();const r=(s=this.settings)==null?void 0:s[t];if(r===void 0){if(n)return n;throw`UI setting "${t}" does not exist and no default was provided.`}return r}}N(D,"settings",null),N(D,"promise",null),N(D,"baseUrl",ut());const kr={getFeatureFlags:()=>D.get("flags")};class Qn extends Le{constructor(){super(...arguments);N(this,"routePrefix","/admin")}getSettings(){return this.get("/settings").then(({data:n})=>n)}async getVersion(){return await this.get("/version").then(({data:n})=>n)}async authCheck(){if(!await D.get("auth"))return 200;try{return(await this.get("/version")).status}catch(r){return r.response?r.response.status:500}}}const Zn={class:"flex flex-col gap-2"},Yn=lt({__name:"ApiStatusToast",setup(e){return(t,n)=>(dt(),ft("div",Zn,[we(Ee(pt),{heading:6},{default:ge(()=>n[0]||(n[0]=[Re(" Oops. Something went wrong. ")])),_:1,__:[0]}),n[2]||(n[2]=be("div",null," You can use Prefect Cloud for free, anytime. ",-1)),be("div",null,[we(Ee(ht),{to:"https://app.prefect.cloud/auth/sign-up?utm_source=prefect-oss&utm_medium=toast",variant:"default",target:"_blank"},{default:ge(()=>n[1]||(n[1]=[Re(" Try it now ")])),_:1,__:[1]})])]))}}),er=[500],tr=[401,403],nr=["ERR_NETWORK"],ie=mt(!1);function rr(e){if(ie.value)return;const t=e.interceptors.response.use(void 0,r);function n(o){var c;if(!it(o)||!o.code)return!1;const i=(c=o.response)==null?void 0:c.status;return typeof i=="number"?tr.includes(i)||er.some(f=>i>=f&&i<f+100):nr.includes(o.code)}function r(o){return n(o)&&!ie.value&&(ie.value=!0,yt(Yn,"error",{dismissible:!0,timeout:!1}),s()),Promise.reject(o)}function s(){e.interceptors.response.eject(t)}}class sr extends wt{async getAutomation(t){const{data:n}=await this.get(`/${t}`);return F.map("AutomationResponse",n,"Automation")}async getAutomations(t={}){const{data:n}=await this.post("/filter",t);return F.map("AutomationResponse",n,"Automation")}async createAutomation(t){const n=F.map("AutomationCreate",t,"AutomationCreateRequest"),{data:r}=await this.post("/",n);return F.map("AutomationResponse",r,"Automation")}updateAutomation(t,n){const r=F.map("AutomationCreate",n,"AutomationCreateRequest");return this.put(`/${t}`,r)}async getResourceAutomations(t){const{data:n}=await this.get(`related-to/${t}`);return F.map("AutomationResponse",n,"Automation")}}const or=1;class ir extends Le{constructor(n,r=null){super(n,r);N(this,"csrfToken");N(this,"clientId",bt());N(this,"refreshTimeout",null);N(this,"ongoingRefresh",null);this.startBackgroundTokenRefresh()}async addCsrfHeaders(n){if(!await D.get("csrfEnabled"))return;const s=await this.getCsrfToken();n.headers["Prefect-Csrf-Token"]=s.token,n.headers["Prefect-Csrf-Client"]=this.clientId,n.headers["Prefect-Csrf-Retry-Count"]=n.headers["Prefect-Csrf-Retry-Count"]??"0"}async getCsrfToken(){if(this.shouldRefreshToken()&&await this.refreshCsrfToken(),!this.csrfToken)throw new Error("CSRF token not available");return this.csrfToken}refreshCsrfToken(n=!1){if(!n&&!this.shouldRefreshToken())return this.ongoingRefresh??Promise.resolve();if(this.ongoingRefresh)return this.ongoingRefresh;const r=async()=>{try{const s=localStorage.getItem("prefect-password"),o=await this.get(`/csrf-token?client=${this.clientId}`,{headers:s?{Authorization:`Basic ${s}`}:void 0});this.csrfToken=F.map("CsrfTokenResponse",o.data,"CsrfToken"),this.ongoingRefresh=null}catch(s){throw this.ongoingRefresh=null,s}};return this.ongoingRefresh=r(),this.ongoingRefresh}shouldRefreshToken(){return this.csrfToken?new Date>this.csrfToken.expiration:!0}async startBackgroundTokenRefresh(){if(!await D.get("csrfEnabled"))return;const r=()=>{if(this.csrfToken){const o=new Date,i=new Date(this.csrfToken.expiration),c=this.csrfToken.issued,f=i.getTime()-c.getTime();return c.getTime()+f*.75-o.getTime()}return 0},s=async()=>{await this.refreshCsrfToken(!0),this.refreshTimeout=setTimeout(s,r())};this.refreshTimeout=setTimeout(s,r())}}function ar(e){return gt(e)?e.response.status===403&&e.response.data.detail.includes("Invalid CSRF token"):!1}function cr(e,t){t.interceptors.request.use(async n=>{var o;const r=(o=n.method)==null?void 0:o.toLowerCase();r&&["post","patch","put","delete"].includes(r)&&await e.addCsrfHeaders(n);const s=localStorage.getItem("prefect-password");return s&&(n.headers.Authorization=`Basic ${s}`),n}),t.interceptors.response.use(void 0,async n=>{if(it(n)&&ar(n)){const{config:r}=n;if(r!=null&&r.headers["Prefect-Csrf-Retry-Count"]){const s=parseInt(r.headers["Prefect-Csrf-Retry-Count"],10);if(s<or)return await e.addCsrfHeaders(r),r.headers["Prefect-Csrf-Retry-Count"]=(s+1).toString(),t(r)}}return Promise.reject(n)})}function Nr(e){const t=ne(new ir(e));function n(s){rr(s),cr(t,s);const o=localStorage.getItem("prefect-password");o&&(s.defaults.headers.common.Authorization=`Basic ${o}`)}return{...Rt(e,n),csrf:t,admin:ne(new Qn(e,n)),automations:ne(new sr(e,n))}}const _r=Symbol("PrefectApi");export{D as U,Nr as c,_r as p,kr as u};
//# sourceMappingURL=api-DGOAIix_.js.map
