{"version": 3, "file": "Event-DyivDqiN.js", "sources": ["../../src/pages/Event.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"event\" class=\"event\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <WorkspaceEventMenu :event=\"event\" />\n        </template>\n      </PageHeading>\n    </template>\n\n    <WorkspaceEventDetails :event />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { Crumb } from '@prefecthq/prefect-design'\n  import { PageHeading, parseRouteDate, WorkspaceEventMenu, WorkspaceEventDetails, useWorkspaceApi, useWorkspaceRoutes, useTimeScopedWorkspaceEventsFilter } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const routes = useWorkspaceRoutes()\n  const date = useRouteParam('eventDate')\n  const eventId = useRouteParam('eventId')\n  const eventDate = computed(() => parseRouteDate(date.value))\n  const filter = useTimeScopedWorkspaceEventsFilter({ startDate: eventDate, eventId: [eventId.value] })\n  const event = await api.events.getFirstEvent(filter.value)\n\n  const crumbs = computed<Crumb[]>(() => [\n    { text: 'Event Feed', to: routes.events() },\n    { text: event.eventLabel },\n  ])\n\n  usePageTitle(`Event: ${event.eventLabel}`)\n</script>"], "names": ["api", "useWorkspaceApi", "routes", "useWorkspaceRoutes", "date", "useRouteParam", "eventId", "eventDate", "computed", "parseRouteDate", "filter", "useTimeScopedWorkspaceEventsFilter", "event", "__temp", "__restore", "_withAsyncContext", "crumbs", "usePageTitle", "_unref", "_createBlock", "_component_p_layout_default", "_createVNode", "PageHeading", "WorkspaceEventMenu", "WorkspaceEventDetails"], "mappings": "mQAqBE,MAAMA,EAAMC,EAAgB,EACtBC,EAASC,EAAmB,EAC5BC,EAAOC,EAAc,WAAW,EAChCC,EAAUD,EAAc,SAAS,EACjCE,EAAYC,EAAS,IAAMC,EAAeL,EAAK,KAAK,CAAC,EACrDM,EAASC,EAAmC,CAAE,UAAWJ,EAAW,QAAS,CAACD,EAAQ,KAAK,EAAG,EAC9FM,GAAQ,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAMf,EAAI,OAAO,cAAcU,EAAO,KAAK,CAAA,mBAEnDM,EAASR,EAAkB,IAAM,CACrC,CAAE,KAAM,aAAc,GAAIN,EAAO,QAAS,EAC1C,CAAE,KAAMU,EAAM,UAAW,CAAA,CAC1B,EAEY,OAAAK,EAAA,UAAUL,EAAM,UAAU,EAAE,+CAjCjBM,EAAKN,CAAA,OAA7BO,EAUmBC,EAAA,OAVY,MAAM,OAAA,GACxB,SACT,IAIc,CAJdC,EAIcH,EAAAI,CAAA,EAAA,CAJA,OAAQN,EAAM,OAAA,CACf,UACT,IAAqC,CAArCK,EAAqCH,EAAAK,CAAA,EAAA,CAAhB,MAAOL,EAAKN,CAAA,GAAA,KAAA,EAAA,CAAA,OAAA,CAAA,CAAA,kCAKvC,IAAgC,CAAhCS,EAAgCH,EAAAM,CAAA,EAAA,CAAR,MAAAN,EAAKN,CAAA,GAAA,KAAA,EAAA,CAAA,OAAA,CAAA,CAAA"}